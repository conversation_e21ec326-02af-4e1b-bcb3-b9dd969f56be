'use client'

import {
  Settings as SettingsIcon,
  Moon,
  Sun,
  Monitor,
  User,
  Store,
  Bell,
  Shield,
  Database,
  Palette,
  Globe,
  Save,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Info,
  Eye,
  EyeOff
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/components'

interface SettingsProps {}

interface StoreSettings {
  storeName: string
  storeDescription: string
  currency: string
  timezone: string
  language: string
  lowStockThreshold: number
  autoBackup: boolean
  emailNotifications: boolean
  pushNotifications: boolean
}

interface UserPreferences {
  defaultView: 'grid' | 'list'
  itemsPerPage: number
  showTutorials: boolean
  compactMode: boolean
  autoSave: boolean
}

export default function Settings({}: SettingsProps) {
  const { resolvedTheme, setTheme } = useTheme()
  const { user } = useAuth()
  const { addToast } = useToast()
  const [mounted, setMounted] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  // Settings state
  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    storeName: 'Revantad Store',
    storeDescription: 'Your friendly neighborhood sari-sari store',
    currency: 'PHP',
    timezone: 'Asia/Manila',
    language: 'en',
    lowStockThreshold: 10,
    autoBackup: true,
    emailNotifications: true,
    pushNotifications: false
  })

  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    defaultView: 'grid',
    itemsPerPage: 12,
    showTutorials: true,
    compactMode: false,
    autoSave: true
  })

  useEffect(() => {
    setMounted(true)
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load settings from localStorage or API
      const savedStoreSettings = localStorage.getItem('store-settings')
      const savedUserPreferences = localStorage.getItem('user-preferences')

      if (savedStoreSettings) {
        setStoreSettings(JSON.parse(savedStoreSettings))
      }
      if (savedUserPreferences) {
        setUserPreferences(JSON.parse(savedUserPreferences))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const saveSettings = async () => {
    setIsLoading(true)
    try {
      // Save to localStorage (in a real app, this would be an API call)
      localStorage.setItem('store-settings', JSON.stringify(storeSettings))
      localStorage.setItem('user-preferences', JSON.stringify(userPreferences))
      
      addToast({
        type: 'success',
        message: 'Settings saved successfully!'
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      addToast({
        type: 'error',
        message: 'Failed to save settings. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
      setStoreSettings({
        storeName: 'Revantad Store',
        storeDescription: 'Your friendly neighborhood sari-sari store',
        currency: 'PHP',
        timezone: 'Asia/Manila',
        language: 'en',
        lowStockThreshold: 10,
        autoBackup: true,
        emailNotifications: true,
        pushNotifications: false
      })
      setUserPreferences({
        defaultView: 'grid',
        itemsPerPage: 12,
        showTutorials: true,
        compactMode: false,
        autoSave: true
      })
      addToast({
        type: 'info',
        message: 'Settings reset to default values'
      })
    }
  }

  const exportSettings = () => {
    const settingsData = {
      storeSettings,
      userPreferences,
      theme: resolvedTheme,
      exportDate: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(settingsData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `revantad-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    addToast({
      type: 'success',
      message: 'Settings exported successfully!'
    })
  }

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'store', label: 'Store', icon: Store },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'data', label: 'Data & Backup', icon: Database }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">General Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Default View
                  </label>
                  <select
                    value={userPreferences.defaultView}
                    onChange={(e) => setUserPreferences(prev => ({ ...prev, defaultView: e.target.value as 'grid' | 'list' }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="grid">Grid View</option>
                    <option value="list">List View</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Items Per Page
                  </label>
                  <select
                    value={userPreferences.itemsPerPage}
                    onChange={(e) => setUserPreferences(prev => ({ ...prev, itemsPerPage: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value={6}>6 items</option>
                    <option value={12}>12 items</option>
                    <option value={24}>24 items</option>
                    <option value={48}>48 items</option>
                  </select>
                </div>
              </div>
              <div className="mt-6 space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={userPreferences.showTutorials}
                    onChange={(e) => setUserPreferences(prev => ({ ...prev, showTutorials: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Show tutorials and tips</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={userPreferences.compactMode}
                    onChange={(e) => setUserPreferences(prev => ({ ...prev, compactMode: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Compact mode</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={userPreferences.autoSave}
                    onChange={(e) => setUserPreferences(prev => ({ ...prev, autoSave: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-save changes</span>
                </label>
              </div>
            </div>
          </div>
        )
      case 'appearance':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Appearance Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Theme
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'light', label: 'Light', icon: Sun },
                      { value: 'dark', label: 'Dark', icon: Moon },
                      { value: 'system', label: 'System', icon: Monitor }
                    ].map(({ value, label, icon: Icon }) => (
                      <button
                        key={value}
                        onClick={() => setTheme(value)}
                        className={`flex flex-col items-center p-4 rounded-lg border-2 transition-colors ${
                          resolvedTheme === value
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                        }`}
                      >
                        <Icon className="h-6 w-6 mb-2" />
                        <span className="text-sm font-medium">{label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="text-center py-12">
            <SettingsIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {tabs.find(tab => tab.id === activeTab)?.label || 'Settings'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              This section is coming soon. Stay tuned for more features!
            </p>
          </div>
        )
    }
  }

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your store preferences and configuration
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={exportSettings}
            className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={resetSettings}
            className="flex items-center px-4 py-2 text-red-700 dark:text-red-400 border border-red-300 dark:border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </button>
          <button
            onClick={saveSettings}
            disabled={isLoading}
            className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </button>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:w-64 flex-shrink-0">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    isActive
                      ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  )
}
